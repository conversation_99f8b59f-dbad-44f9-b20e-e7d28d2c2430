#pragma once

#include "stream_link.h"
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>

namespace SPipeline {

/**
 * AsyncBridge - A thread-safe link that buffers data for cross-thread communication
 * with configurable buffer size.
 * 
 * This link provides thread-safe buffering for scenarios where data needs to be passed
 * between different threads. It uses a queue with configurable maximum size and blocks
 * when the buffer is full.
 */
template<typename TData>
class AsyncBridge final : public StreamLink<TData, TData> {
private:
    std::queue<TData> buffer_; // !
    mutable std::mutex bufferMutex_; // !
    std::condition_variable dataAvailable_;
    std::condition_variable spaceAvailable_;
    std::atomic<bool> isShutdown_{false};
    size_t maxBufferSize_;
    
public:
    using StreamLink<TData, TData>::outputCallback_; // TODO Remove

    /**
     * Constructor
     * @param maxBufferSize Maximum number of items to buffer (default: 100)
     */
    explicit AsyncBridge(size_t maxBufferSize = 100): maxBufferSize_(maxBufferSize) {
    }
    
    /**
     * Destructor - ensures proper shutdown
     */
    ~AsyncBridge() override {
        shutdown();
    }
    
    /**
     * IStreamLinkInput<TData> implementation
     * Adds data to the buffer. Blocks if buffer is full until space becomes available
     * or shutdown is called.
     */
    void forward(TData&& data) override {
        std::unique_lock<std::mutex> lock(bufferMutex_);
        
        // Wait for space in buffer or shutdown
        spaceAvailable_.wait(lock, [this] {
            return buffer_.size() < maxBufferSize_ || isShutdown_.load();
        });
        
        buffer_.push(std::move(data));
        dataAvailable_.notify_one();
    }
    
    /**
     * IStreamLinkOutput<TData> implementation
     * Registers the callback function that will receive processed data.
     */
    void onOutput(typename IStreamLinkOutput<TData>::OutputCallback callback) override {
        std::lock_guard<std::mutex> lock(bufferMutex_);
        outputCallback_ = std::move(callback);
    }
    
    /**
     * Tickable implementation
     * Processes all buffered data by calling the output callback.
     * Returns true if there might be more work to do.
     */
    bool tick() override {
        std::unique_lock<std::mutex> lock(bufferMutex_);
        
        // Process all available data
        while (!buffer_.empty()) {
            TData data = std::move(buffer_.front());
            buffer_.pop();
            
            // Notify that space is available
            spaceAvailable_.notify_one();
            
            // Unlock before calling callback to prevent deadlock
            lock.unlock();
            outputCallback_(std::move(data));
            lock.lock();
        }
        
        return !isShutdown_.load();  // Continue ticking unless shutdown
    }
    
    /**
     * Tickable implementation
     * Returns true if there is buffered data waiting to be processed.
     */
    bool hasPendingWork() const override {
        std::lock_guard<std::mutex> lock(bufferMutex_);
        return !buffer_.empty();
    }
    
    /**
     * Initiates shutdown of the bridge.
     * Wakes up any threads waiting on the buffer and prevents new data from being added.
     */
    void shutdown() {
        isShutdown_.store(true);
        spaceAvailable_.notify_all();
        dataAvailable_.notify_all();
    }
};

} // namespace SPipeline
