#pragma once

#include "stream_link.h"
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>

namespace SPipeline {

/**
 * AsyncBridge - A thread-safe link that buffers data for cross-thread communication
 * with configurable buffer size.
 * 
 * This link provides thread-safe buffering for scenarios where data needs to be passed
 * between different threads. It uses a queue with configurable maximum size and blocks
 * when the buffer is full.
 */
template<typename TData>
class AsyncBridge final : public StreamLink<TData, TData> {
private:
    std::queue<TData> buffer_;
    mutable std::mutex bufferMutex_;
    std::condition_variable dataAvailable_;
    std::condition_variable spaceAvailable_;
    std::atomic<bool> isShutdown_{false};
    std::chrono::milliseconds tickTimeout_;
    size_t maxBufferSize_;

public:
    using StreamLink<TData, TData>::outputCallback_;

    /**
     * Constructor
     * @param maxBufferSize Maximum number of items to buffer (default: 100)
     * @param tickTimeout Timeout for tick() method blocking (default: 100ms)
     */
    explicit AsyncBridge(size_t maxBufferSize = 100, std::chrono::milliseconds tickTimeout = std::chrono::milliseconds(100))
        : maxBufferSize_(maxBufferSize), tickTimeout_(tickTimeout) {
    }
    
    /**
     * Destructor - ensures proper shutdown
     */
    ~AsyncBridge() override {
        shutdown();
    }
    
    /**
     * IStreamLinkInput<TData> implementation
     * Adds data to the buffer if space is available and not shutting down.
     * @param data Data to forward
     * @return true if data was successfully added, false if buffer full or shutting down
     */
    bool forward(TData&& data) override {
        std::lock_guard<std::mutex> lock(bufferMutex_);

        // Check if we have space and are not shutting down
        if (buffer_.size() >= maxBufferSize_ || isShutdown_.load()) {
            return false;
        }

        buffer_.push(std::move(data));
        dataAvailable_.notify_one();
        return true;
    }
    
    /**
     * IStreamLinkOutput<TData> implementation
     * Registers the callback function that will receive processed data.
     */
    void onOutput(typename IStreamLinkOutput<TData>::OutputCallback callback) override {
        std::lock_guard<std::mutex> lock(bufferMutex_);
        outputCallback_ = std::move(callback);
    }
    
    /**
     * Tickable implementation
     * Blocks until data is available or timeout expires, then processes all buffered data.
     * @return true to continue ticking, false to stop (shutdown or callback failure)
     */
    bool tick() override {
        std::unique_lock<std::mutex> lock(bufferMutex_);

        // Block until data is available or timeout expires
        auto dataReceived = dataAvailable_.wait_for(lock, tickTimeout_, [this] {
            return !buffer_.empty() || isShutdown_.load();
        });

        if (isShutdown_.load()) {
            return false; // If shutting down, return false to stop ticking
        }

        if (!dataReceived) {
            return true; // If timeout expired without data available, just return true to continue ticking
        }

        // Process all available data
        while (!buffer_.empty()) {
            TData data = std::move(buffer_.front());
            buffer_.pop();
            
            // Notify that space is available
            spaceAvailable_.notify_one();
            
            // Unlock before calling callback to prevent deadlock
            lock.unlock();

            // Call output callback and check return value
            auto continueProcessing = outputCallback_(std::move(data));
            if (!continueProcessing) {
                // Callback signaled to stop - set shutdown flag and return false
                isShutdown_.store(true);
                return false;
            }
            lock.lock();
        }

        return !isShutdown_.load();  // Continue ticking unless shutdown
    }
    
    /**
     * Tickable implementation
     * Returns true if there is buffered data waiting to be processed.
     */
    bool hasPendingWork() const override {
        std::lock_guard<std::mutex> lock(bufferMutex_);
        return !buffer_.empty();
    }
    
    /**
     * Initiates shutdown of the bridge.
     * Wakes up any threads waiting on the buffer and prevents new data from being added.
     */
    void shutdown() override {
        isShutdown_.store(true);
        spaceAvailable_.notify_all();
        dataAvailable_.notify_all();
    }
};

} // namespace SPipeline
