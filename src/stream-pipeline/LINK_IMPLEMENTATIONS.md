# Stream Pipeline Link Implementations

This document describes the PassthroughLink and AsyncBridge implementations for the Stream Processing Pipeline Framework.

## Overview

Two concrete link implementations have been added to provide common data flow patterns:

1. **PassthroughLink** - Synchronous, zero-copy forwarding for same-thread connections
2. **AsyncBridge** - Thread-safe buffering for cross-thread communication

## PassthroughLink

### Purpose
A synchronous, zero-copy link that forwards data of the same type without transformation or buffering. Ideal for high-frequency data paths where minimal overhead is required.

### Features
- **Zero-copy operation**: Uses move semantics throughout
- **Minimal overhead**: No buffering or processing delays
- **Same-type forwarding**: Input and output types are identical
- **Immediate forwarding**: Data is passed to output callback immediately

### Usage
```cpp
#include "stream-pipeline/passthrough_link.h"

PassthroughLink<MyDataType> link;

// Set up output callback
link.onOutput([](MyDataType&& data) {
    // Process received data
    std::cout << "Received: " << data.value << std::endl;
});

// Forward data
MyDataType data(42);
link.forward(std::move(data));  // Immediately calls output callback
```

### Performance Characteristics
- **Latency**: Minimal (immediate forwarding)
- **Memory**: No additional memory allocation
- **Thread safety**: Not thread-safe (designed for same-thread use)

## AsyncBridge

### Purpose
A thread-safe link that buffers data for cross-thread communication with configurable buffer size. Provides safe data transfer between different threads with flow control.

### Features
- **Thread-safe buffering**: Uses mutex and condition variables
- **Configurable buffer size**: Prevents unbounded memory growth
- **Flow control**: Blocks when buffer is full
- **Graceful shutdown**: Proper cleanup and thread synchronization
- **Deadlock prevention**: Unlocks mutex before calling output callbacks

### Usage
```cpp
#include "stream-pipeline/async_bridge.h"

// Create bridge with buffer size of 100 items
AsyncBridge<MyDataType> bridge(100);

// Set up output callback (called from tick() thread)
bridge.onOutput([](MyDataType&& data) {
    // Process received data in consumer thread
    std::cout << "Processed: " << data.value << std::endl;
});

// Producer thread: Add data to buffer
MyDataType data(42);
bridge.forward(std::move(data));  // May block if buffer is full

// Consumer thread: Process buffered data
while (bridge.hasPendingWork()) {
    bridge.tick();  // Processes all available data
}

// Shutdown
bridge.shutdown();  // Wakes up blocked threads and prevents new data
```

### Threading Model
- **Producer threads**: Call `forward()` to add data to buffer
- **Consumer thread**: Calls `tick()` to process buffered data
- **Buffer management**: Automatic blocking when full, notification when space available

### Performance Characteristics
- **Latency**: Depends on tick frequency and buffer size
- **Memory**: Bounded by maxBufferSize parameter
- **Thread safety**: Fully thread-safe
- **Blocking behavior**: Producers block when buffer is full

## Integration with Stream Pipeline

Both link implementations inherit from `StreamLink<TInput, TOutput>` and can be used interchangeably in pipeline configurations:

```cpp
// Same-thread connection
PassthroughLink<SampleData> passthroughLink;
sourceNode.connectOutputLink(&passthroughLink);
passthroughLink.onOutput([&processorNode](SampleData&& data) {
    processorNode.process(std::move(data));
});

// Cross-thread connection
AsyncBridge<ProcessedData> asyncBridge(50);
processorNode.connectOutputLink(&asyncBridge);
asyncBridge.onOutput([&sinkNode](ProcessedData&& data) {
    sinkNode.process(std::move(data));
});

// Tick loop for async processing
while (running) {
    asyncBridge.tick();
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
}
```

## Examples

### Simple Test
See `examples/simple_test.cpp` for basic functionality verification.

### Complete Pipeline
See `examples/basic_pipeline.cpp` for a comprehensive example showing:
- Multi-threaded pipeline setup
- Same-thread and cross-thread connections
- Proper shutdown sequence
- Tick management

### Building Examples
```bash
cd src/stream-pipeline/examples
make test          # Run simple functionality test
make run-basic     # Run complete pipeline example
make clean         # Clean build artifacts
```

## Best Practices

### When to Use PassthroughLink
- Same-thread data flow
- High-frequency, low-latency requirements
- No data transformation needed
- Minimal memory overhead required

### When to Use AsyncBridge
- Cross-thread data transfer
- Producer/consumer scenarios
- Need for flow control and buffering
- Different processing rates between components

### Buffer Sizing for AsyncBridge
- **Small buffers (10-50)**: Low latency, risk of blocking producers
- **Medium buffers (50-200)**: Balanced latency and throughput
- **Large buffers (200+)**: High throughput, higher memory usage and latency

### Shutdown Sequence
1. Stop data sources
2. Allow pending data to flow through pipeline
3. Call `shutdown()` on AsyncBridge instances
4. Stop tick threads
5. Clean up resources

## Thread Safety Notes

- **PassthroughLink**: Not thread-safe, designed for same-thread use
- **AsyncBridge**: Fully thread-safe for all operations
- **Callbacks**: Output callbacks are called from the thread that calls `tick()`
- **Deadlock prevention**: AsyncBridge unlocks mutex before calling callbacks
