#pragma once

namespace SPipeline {

/**
 * PipelineComponent - Base interface for components that can be ticked for processing
 */
class PipelineComponent {
public:
    virtual ~PipelineComponent() = default;

    /**
     * Perform one processing cycle
     * @return true to continue processing, false to stop (shutdown or error)
     */
    virtual bool tick() { return false; }

    /**
     * Check if the component has pending work
     * @return true if there is work to be done, false otherwise
     */
    [[nodiscard]] virtual bool hasPendingWork() const { return false; }

    /**
     * Initiate shutdown of the component
     * Should be implemented by components that need graceful shutdown
     */
    virtual void shutdown() {}
};

} // namespace SPipeline
